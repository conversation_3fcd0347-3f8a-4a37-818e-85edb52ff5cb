<template>
  <div>
    <a-spin :spinning="spinning">
      <a-form :model="queryParams" :label-col="{ span: 8 }" :wrapper-col="{ span: 16 }">
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="用户">
              <a-input v-model:value="queryParams.open_id" placeholder="输入用户ID" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="项目">
              <a-input v-model:value="queryParams.project_id" placeholder="输入项目ID" />
            </a-form-item>
          </a-col>
          <a-col :span="8">
            <a-form-item label="广告">
              <a-input v-model:value="queryParams.promotion_id" placeholder="输入广告ID" />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row :gutter="16">
          <a-col :span="8">
            <a-form-item label="时间">
              <a-range-picker v-model:value="queryParams.timeRange" />
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-form-item>
              <a-button type="primary" @click="handleSearch">查询</a-button>
            </a-form-item>
          </a-col>
          <a-col :span="3">
            <a-form-item>
              <a-button type="primary" @click="clearQueryParams" ghost>重置</a-button>
            </a-form-item>
          </a-col>
          <a-col :span="2">
            <a-form-item>
              <a-button type="primary" @click="exportData">导出</a-button>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <a-table
        :columns="columns"
        :data-source="data"
        :pagination="pagination"
        :row-key="(record) => record.uuid"
        bordered
        size="small"
      >
        <template #bodyCell="{ column, record }">
          <span v-if="column.dataIndex === 'os'">
            <template v-if="record.os === 0">
              <AndroidOutlined />
            </template>
            <template v-else>
              <AppleOutlined />
            </template>
            {{ record.os === 0 ? '安卓' : '苹果' }}
          </span>
          <span v-else-if="column.dataIndex === 'time_stamp'">
            {{ formatDate(record.time_stamp) }}
          </span>
          <span v-else-if="column.dataIndex === 'key_ed'">
            <template v-if="record.key_ed === 0">
              <a-button type="link" @click="openModal(record)">手动上报</a-button>
            </template>
            <template v-else>
              {{ record.key_ed }}
            </template>
          </span>
        </template>
      </a-table>

      <!-- 手动上报确认弹窗 -->
      <a-modal
        v-model:visible="isModalVisible"
        title="确认手动上报"
        @ok="handleManualReport"
        @cancel="handleCancel"
      >
        <p>是否确认手动上报该条记录？</p>
      </a-modal>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, ref, watch } from 'vue'
import { getGameUserList, updateReportStatus } from '@/api/api' // 假设存在这个接口
import { useStore } from 'vuex'
import { type NavigationGuardNext, type RouteLocationNormalized, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import moment, { Moment } from 'moment'

export default defineComponent({
  computed: {
    id(): string | undefined {
      const param = this.$route.params.id
      if (typeof param === 'string') {
        return param
      } else if (Array.isArray(param)) {
        return param.length > 0 ? param[0] : undefined
      }
      return undefined
    }
  },
  setup() {
    const store = useStore()
    const route = useRoute()

    const userData = computed(() => store.getters.userData)
    const id = computed(() => route.params.id as string | undefined)
    const spinning = ref(true)
    const isModalVisible = ref(false)
    const selectedRecord = ref(null)
    const data = ref([])
    const columns = ref([
      {
        title: '序号',
        dataIndex: 'uuid',
        width: 60
      },
      {
        title: '用户id',
        dataIndex: 'open_id',
        width: 230
      },
      {
        title: '项目id',
        dataIndex: 'project_id'
      },
      {
        title: '广告id',
        dataIndex: 'promotion_id'
      },
      {
        title: 'ecpm',
        dataIndex: 'aveEcpm'
      },
      {
        title: '曝光数',
        dataIndex: 'exposureCnt'
      },
      {
        title: '总ECPM',
        dataIndex: 'ecpm'
      },
      {
        title: '已上报',
        dataIndex: 'key_ed',
        width: 100
      },
      {
        title: '平台',
        dataIndex: 'os',
        width: 100
      },
      {
        title: '时间',
        dataIndex: 'time_stamp'
      }
    ])

    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
      onChange: (page: number, pageSize?: number) => {
        pagination.value.current = page
        pagination.value.pageSize = pageSize || 10
        fetchData()
      },
      onShowSizeChange: (current: number, size: number) => {
        pagination.value.current = 1
        pagination.value.pageSize = size
        fetchData()
      }
    })

    const queryParams = ref({
      open_id: '',
      promotion_id: '',
      project_id: '',
      timeRange: [] as Moment[],
      ltv: ''
    })

    const fetchData = async () => {
      console.log('id', id.value)
      console.log(route.params.id, '路由参数')
      try {
        const startTime = queryParams.value.timeRange[0]
          ? moment(queryParams.value.timeRange[0].valueOf()).format('YYYY-MM-DD')
          : undefined
        const endTime = queryParams.value.timeRange[1]
          ? moment(queryParams.value.timeRange[1].valueOf()).format('YYYY-MM-DD')
          : undefined
        console.log(startTime, endTime, '获取用户列表参数')
        const res = await getGameUserList(
          userData.value.account,
          id.value,
          pagination.value.current,
          pagination.value.pageSize,
          '',
          false,
          queryParams.value.open_id,
          queryParams.value.project_id,
          queryParams.value.promotion_id,
          startTime?.toString(), // 确保 startTime 是字符串类型
          endTime?.toString() // 确保 endTime 是字符串类型
        )
        console.log(res, '获取用户列表成功')
        spinning.value = false
        data.value = res.data.list
        pagination.value.total = res.data.total
      } catch (error) {
        spinning.value = false
        console.log(error, '获取用户列表失败')
        message.error('获取用户列表失败')
      }
    }

    const formatDate = (timestamp: number): string => {
      const date = new Date(timestamp)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    }

    const handleSearch = () => {
      spinning.value = true // 开始加载
      pagination.value.current = 1
      fetchData().finally(() => {
        spinning.value = false // 结束加载
      })
    }

    const clearQueryParams = () => {
      queryParams.value = {
        open_id: '',
        promotion_id: '',
        project_id: '',
        timeRange: [] as Moment[],
        ltv: ''
      }
    }

    const exportData = async () => {
      try {
        const startTime = queryParams.value.timeRange[0]
          ? moment(queryParams.value.timeRange[0].valueOf()).format('YYYY-MM-DD')
          : undefined
        const endTime = queryParams.value.timeRange[1]
          ? moment(queryParams.value.timeRange[1].valueOf()).format('YYYY-MM-DD')
          : undefined
        console.log(startTime, endTime, '导出数据')
        const res = await getGameUserList(
          userData.value.account,
          id.value,
          pagination.value.current,
          pagination.value.pageSize,
          '',
          false,
          queryParams.value.open_id,
          queryParams.value.project_id,
          queryParams.value.promotion_id,
          startTime?.toString(), // 确保 startTime 是字符串类型
          endTime?.toString() // 确保 endTime 是字符串类型
        )
        // 假设 res.data.exportUrl 是导出文件的 URL
        const exportUrl = res.data.exportUrl
        window.open(exportUrl, '_blank')
      } catch (error) {
        console.log(error, '导出数据失败')
        message.error('导出数据失败')
      }
    }

    const openModal = (record: any) => {
      selectedRecord.value = record
      isModalVisible.value = true
    }

    const handleManualReport = async () => {
      try {
        if (selectedRecord.value) {
          const appid = route.params.id as string
          // console.log('App ID:', appid);
          const res = await updateReportStatus(
            userData.value.account,
            appid,
            selectedRecord.value.open_id,
            1
          )
          if (res) {
            message.success('上报成功')
            fetchData() // 重新获取数据以刷新表格
          } else {
            message.error('上报失败')
          }
        }
      } catch (error) {
        console.log(error)
        message.error('上报失败')
      } finally {
        isModalVisible.value = false
      }
    }

    const handleCancel = () => {
      isModalVisible.value = false
    }

    onMounted(() => {
      fetchData()
      console.log('mounted')
    })

    watch(
      () => route.params.id,
      (newId, oldId) => {
        console.log('路由参数变化', newId, oldId)
        fetchData()
      }
    )

    return {
      id,
      columns,
      spinning,
      data,
      pagination,
      formatDate,
      fetchData,
      queryParams,
      handleSearch,
      clearQueryParams,
      exportData,
      isModalVisible,
      openModal,
      handleManualReport,
      handleCancel
    }
  },
  beforeRouteUpdate(
    to: RouteLocationNormalized,
    from: RouteLocationNormalized,
    next: NavigationGuardNext
  ) {
    // 监听路由参数变化
    ;(this as any).fetchData()
    ;(this as any).pagination.current = 1
    ;(this as any).pagination.pageSize = 10
    next()
  },
  filters: {
    osType(value: number) {
      if (value == 0) {
        return '安卓'
      } else if (value == 1) {
        return '苹果'
      } else {
        return '其他'
      }
    }
  }
})
</script>

<style lang="scss" scoped></style>
