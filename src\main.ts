import './assets/css/main.scss'
import './assets/iconfont/iconfont.css'
import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import Antd from 'ant-design-vue'
import 'ant-design-vue/dist/antd.css'
import * as Icons from '@ant-design/icons-vue'
import zhCN from 'ant-design-vue/es/locale/zh_CN';
import moment from 'moment';
import 'moment/locale/zh-cn';
import dayjs from 'dayjs';
import 'dayjs/locale/zh-cn';

dayjs.locale('zh-cn');


moment.locale('zh-cn');
const app = createApp(App)

app.use(router)
app.use(store)
app.use(Antd)

app.config.globalProperties.$locale = zhCN;


// 在应用挂载之前进行 token 检查
router.isReady().then(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
    } else {
      store.commit('setAuthenticated', true)
      router.push('/')
    }
  })
app.mount('#app')

const icons = Icons
Object.keys(icons).forEach(i => {
    app.component(i, icons[i as keyof typeof icons])
})

  