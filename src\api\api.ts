import { changeGameListApiDataToNavData } from '@/navDataInfo'
import { BackStageApi } from './BackStageApi'
import meAxios from './meAxios'

export async function logout(account: string): Promise<BackStageApi.Post.Logout.response> {
  try {
    const headers: BackStageApi.Post.Logout.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.Logout.router,
      {
        account
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('登出失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

export async function getGameList(account: string) {
  try {
    console.log('获取游戏列表')
    const headers: BackStageApi.Post.GetGameList.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.GetGameList.router,
      {
        account
      },
      { headers }
    )
    //
    const res: BackStageApi.Post.GetGameList.response = response.data.data
    //
    res.list = res.list.map((item, index) => {
      return changeGameListApiDataToNavData(index, item)
    }) as any
    //
    console.log('转换后', res.list)
    return res
  } catch (error) {
    console.error('获取游戏列表失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

export async function updateReportStatus(
  account: string,
  appid: string,
  openid: string,
  ed: 1 | 0
) {
  try {
    const headers: BackStageApi.Post.ReportEcpmPoint.headers = {
      token: localStorage.getItem('token') || ''
    }
    console.log('上报数据', headers, ...arguments)
    const response = await meAxios.post(
      BackStageApi.Post.ReportEcpmPoint.router,
      {
        account,
        openid,
        appid,
        ed
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('上报数据失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

// 获取游戏用户列表
export async function getGameUserList(
  account: string,
  appid: string,
  start: number,
  limit: number,
  order: string,
  desc: boolean,
  open_id: string, //用户id
  project_id: string, //项目id
  promotion_id: string, //广告id
  start_time: string, //开始时间
  end_time: string //结束时间
) {
  try {
    const headers: BackStageApi.Post.getGameUserList.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.getGameUserList.router,
      {
        account,
        appid,
        start, //当前页
        limit, //每页数量
        order, //排序方式
        desc, //是否倒序
        open_id, //用户id
        project_id, //项目id
        promotion_id, //广告id
        start_time, //开始时间
        end_time //结束时间
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('获取游戏用户列表失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

// 获取直投账户列表
export async function GetPutInAccountList(account: string, page: number, pageSize: number) {
  try {
    const headers: BackStageApi.Post.GetPutInAccountList.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.GetPutInAccountList.router,
      {
        account,
        page, //当前页
        pageSize //每页数量
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('获取直投账户列表失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

// 添加直投账户
export async function AddPutInAccount(
  account: string,
  advertiser_id: string,
  ecpm: number,
  exCnt: number,
  appId: string,
  advertiser_name: string,
  on_off: number
) {
  try {
    const headers: BackStageApi.Post.AddPutInAccount.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.AddPutInAccount.router,
      {
        account,
        advertiser_id,
        ecpm,
        exCnt,
        appId,
        advertiser_name,
        on_off
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('添加直投账户失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

// 修改直投账户
export async function EditPutInAccount(
  account: string,
  advertiser_id: string,
  ecpm: number,
  exCnt: number,
  appId: string,
  advertiser_name: string,
  on_off: number
) {
  try {
    const headers: BackStageApi.Post.EditPutInAccount.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.EditPutInAccount.router,
      {
        account,
        advertiser_id,
        ecpm,
        exCnt,
        appId,
        advertiser_name,
        on_off
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('修改直投账户失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

// 修改直投账户是否启用
export async function UpdatePutInAccount(account: string, advertiser_id: string, on_off: number) {
  try {
    const headers: BackStageApi.Post.EditPutInAccount.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.EditPutInAccount.router,
      {
        account,
        advertiser_id,
        on_off
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('修改直投账户失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}

// 删除直投账户
export async function DeletePutInAccount(account: string, advertiser_id: string) {
  try {
    const headers: BackStageApi.Post.DeletePutInAccount.headers = {
      token: localStorage.getItem('token') || ''
    }
    const response = await meAxios.post(
      BackStageApi.Post.DeletePutInAccount.router,
      {
        account,
        advertiser_id
      },
      { headers }
    )
    return response.data
  } catch (error) {
    console.error('删除直投账户失败', error)
    throw error // 确保错误被抛出，以便拦截器可以捕获
  }
}
