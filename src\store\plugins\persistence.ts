import { Store } from 'vuex';
import { State } from '../state'; // 假设你有一个定义 state 类型的文件

export default (store: Store<State>) => {
  // 从 localStorage 中读取数据并恢复到 store
  const userData = JSON.parse(localStorage.getItem('userData') || '{}');
  if (Object.keys(userData).length > 0) {
    store.commit('setUserData', userData);
  }

  // 订阅 store 的变化并保存到 localStorage
  store.subscribe((mutation, state) => {
    if (mutation.type === 'setUserData') {
      localStorage.setItem('userData', JSON.stringify(state.userData));
    }
  });
};