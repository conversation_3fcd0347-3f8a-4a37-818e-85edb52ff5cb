// src/api/meAxios.ts
import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse, InternalAxiosRequestConfig } from 'axios';
import { message } from 'ant-design-vue';
import store from '@/store';
import { redirectToLogin } from '@/router'; // 导入 redirectToLogin 方法

const instance: AxiosInstance = axios.create({
  baseURL: 'https://back.tobetopone.com', // 正式环境
  // baseURL: 'http://apple.tobetopone.com', // 测试环境
  // baseURL: 'http://192.168.0.106:3000', // 你的服务器地址
  timeout: 10000, // 请求超时时间
});

// 请求拦截器
instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['token'] = `${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器
instance.interceptors.response.use(
  response => {
    // debugger;
    return response;
  },
  error => {
    const { response } = error;
    // debugger;
    if (response && response.status === 401) {
      localStorage.clear();
      store.commit('setAuthenticated', false);
      store.commit('clearUserData'); // 清除 userData
      message.error('登录过期，请重新登录');
      redirectToLogin(); // 调用 redirectToLogin 方法
    } else {
      return Promise.reject(error);
    }
  }
);

export default instance;
