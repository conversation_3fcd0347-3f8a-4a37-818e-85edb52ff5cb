import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from "path"

export default defineConfig({
  plugins: [
    vue(),
  ],
  resolve: {
    alias: {
      // '@': fileURLToPath(new URL('./src', import.meta.url))
      '@': resolve(__dirname, 'src'), // 兼容src目录下的文件夹可通过 @/components/HelloWorld.vue写法 
    }
  },
  build: {
    target: 'esnext', // 设置为esnext以避免转换为JavaScript
    minify: false, // 关闭压缩，以保持文件扩展名为.ts
  },
  server: {
    proxy: {
      '/api': {
        target: 'https://back.tobetopone.com',
        changeOrigin: true,
        rewrite: (path) => path.replace(/^\/api/, '')
      }
    }
  }
})

