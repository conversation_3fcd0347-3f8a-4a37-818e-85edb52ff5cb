// src/router/index.ts
import { createRouter, createWebHistory } from 'vue-router';
import store from '../store';
// export function redirectToLogin() {
//   const router = useRouter();
//   router.push('/login');
// }
let isRedirecting = false;

export function redirectToLogin() {
  if (isRedirecting) return;
  isRedirecting = true;
  // 你的跳转逻辑
  router.push('/login');
  setTimeout(() => {
    isRedirecting = false;
  }, 5000); // 5秒后重置标志
}
const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      meta: {
        title: '登录',
        keepalive: true
      },
      component: () => import('@/views/Login.vue')
    },
    {
      //导航页
      path: '/',
      name: 'layout',
      meta: {
        title: '首页',
        keepalive: true
      },
      component: () => import('@/views/layout/index.vue'),
      children: [
        {
          //欢迎页
          path: '/',
          name: 'welcome',
          meta: {
            title: '首页',
            keepalive: true
          },
          component: () => import('@/views/welcome/index.vue')
        },
        {
          //直投账户
          path: '/runtimeData',
          name: 'runtimeData',
          meta: {
            title: '直投账户',
            keepalive: true
          },
          component: () => import('@/views/runtimeData/index.vue')
        },
        {
          //数据分析
          path: '/dataAnalysis',
          name: 'dataAnalysis',
          meta: {
            title: '数据分析',
            keepalive: true
          },
          component: () => import('@/views/dataAnalysis/index.vue')
        },
        {
          // 用户管理
          path: '/dataManage/:id',
          name: 'dataManage',
          meta: {
            title: '用户管理',
            keepalive: true
          },
          component: () => import('@/views/dataManage/index.vue')
        },
        {
          //查看用户信息
          path: '/showUserInfo',
          name: 'showUserInfo',
          meta: {
            title: '查看用户信息',
            keepalive: true
          },
          component: () => import('@/views/my/showUserInfo.vue')
        },
        {
          //修改用户信息
          path: '/editUserInfo',
          name: 'editUserInfo',
          meta: {
            title: '修改用户信息',
            keepalive: true
          },
          component: () => import('@/views/my/editUserInfo.vue')
        },
      ]
    },
  ]
});

router.beforeEach((to, from, next) => {
  const isAuthenticated = store.getters.isAuthenticated;

  if (to.matched.some(record => record.meta.requiresAuth)) {
    if (!isAuthenticated) {
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      });
    } else {
      next();
    }
  } else {
    next();
  }
});

export default router;
