<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8"/>
  <title>iconfont Demo</title>
  <link rel="shortcut icon" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg" type="image/x-icon"/>
  <link rel="icon" type="image/svg+xml" href="//img.alicdn.com/imgextra/i4/O1CN01Z5paLz1O0zuCC7osS_!!6000000001644-55-tps-83-82.svg"/>
  <link rel="stylesheet" href="https://g.alicdn.com/thx/cube/1.3.2/cube.min.css">
  <link rel="stylesheet" href="demo.css">
  <link rel="stylesheet" href="iconfont.css">
  <script src="iconfont.js"></script>
  <!-- jQuery -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/7bfddb60-08e8-11e9-9b04-53e73bb6408b.js"></script>
  <!-- 代码高亮 -->
  <script src="https://a1.alicdn.com/oss/uploads/2018/12/26/a3f714d0-08e6-11e9-8a15-ebf944d7534c.js"></script>
  <style>
    .main .logo {
      margin-top: 0;
      height: auto;
    }

    .main .logo a {
      display: flex;
      align-items: center;
    }

    .main .logo .sub-title {
      margin-left: 0.5em;
      font-size: 22px;
      color: #fff;
      background: linear-gradient(-45deg, #3967FF, #B500FE);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
    }
  </style>
</head>
<body>
  <div class="main">
    <h1 class="logo"><a href="https://www.iconfont.cn/" title="iconfont 首页" target="_blank">
      <img width="200" src="https://img.alicdn.com/imgextra/i3/O1CN01Mn65HV1FfSEzR6DKv_!!6000000000514-55-tps-228-59.svg">
      
    </a></h1>
    <div class="nav-tabs">
      <ul id="tabs" class="dib-box">
        <li class="dib active"><span>Unicode</span></li>
        <li class="dib"><span>Font class</span></li>
        <li class="dib"><span>Symbol</span></li>
      </ul>
      
      <a href="https://www.iconfont.cn/manage/index?manage_type=myprojects&projectId=332687" target="_blank" class="nav-more">查看项目</a>
      
    </div>
    <div class="tab-container">
      <div class="content unicode" style="display: block;">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
              <span class="icon iconfont">&#xe60a;</span>
                <div class="name">退出登录</div>
                <div class="code-name">&amp;#xe60a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe636;</span>
                <div class="name">返回</div>
                <div class="code-name">&amp;#xe636;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe664;</span>
                <div class="name">位置</div>
                <div class="code-name">&amp;#xe664;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe71e;</span>
                <div class="name">修改</div>
                <div class="code-name">&amp;#xe71e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe81a;</span>
                <div class="name">添加 加号 无边框</div>
                <div class="code-name">&amp;#xe81a;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe60e;</span>
                <div class="name">删除</div>
                <div class="code-name">&amp;#xe60e;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe969;</span>
                <div class="name">关闭</div>
                <div class="code-name">&amp;#xe969;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe613;</span>
                <div class="name">打印</div>
                <div class="code-name">&amp;#xe613;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6c9;</span>
                <div class="name">日期</div>
                <div class="code-name">&amp;#xe6c9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe6b9;</span>
                <div class="name">加</div>
                <div class="code-name">&amp;#xe6b9;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe609;</span>
                <div class="name">导入</div>
                <div class="code-name">&amp;#xe609;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe61f;</span>
                <div class="name">方向</div>
                <div class="code-name">&amp;#xe61f;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe607;</span>
                <div class="name">方向</div>
                <div class="code-name">&amp;#xe607;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe647;</span>
                <div class="name">用户</div>
                <div class="code-name">&amp;#xe647;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe605;</span>
                <div class="name">密码</div>
                <div class="code-name">&amp;#xe605;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe603;</span>
                <div class="name">定位</div>
                <div class="code-name">&amp;#xe603;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe635;</span>
                <div class="name">日历</div>
                <div class="code-name">&amp;#xe635;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe602;</span>
                <div class="name">上三角</div>
                <div class="code-name">&amp;#xe602;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe968;</span>
                <div class="name">下三角</div>
                <div class="code-name">&amp;#xe968;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe62c;</span>
                <div class="name">导出</div>
                <div class="code-name">&amp;#xe62c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe719;</span>
                <div class="name">查询</div>
                <div class="code-name">&amp;#xe719;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe612;</span>
                <div class="name">二氧化碳</div>
                <div class="code-name">&amp;#xe612;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe66c;</span>
                <div class="name">监控视频</div>
                <div class="code-name">&amp;#xe66c;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe606;</span>
                <div class="name">工单管理</div>
                <div class="code-name">&amp;#xe606;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe627;</span>
                <div class="name">设备</div>
                <div class="code-name">&amp;#xe627;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe604;</span>
                <div class="name">森林</div>
                <div class="code-name">&amp;#xe604;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe704;</span>
                <div class="name">告警</div>
                <div class="code-name">&amp;#xe704;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe691;</span>
                <div class="name">报表</div>
                <div class="code-name">&amp;#xe691;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe967;</span>
                <div class="name">煤炭生产</div>
                <div class="code-name">&amp;#xe967;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe600;</span>
                <div class="name">方向</div>
                <div class="code-name">&amp;#xe600;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe601;</span>
                <div class="name">方向</div>
                <div class="code-name">&amp;#xe601;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe667;</span>
                <div class="name">电站数量</div>
                <div class="code-name">&amp;#xe667;</div>
              </li>
          
            <li class="dib">
              <span class="icon iconfont">&#xe608;</span>
                <div class="name">分析</div>
                <div class="code-name">&amp;#xe608;</div>
              </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="unicode-">Unicode 引用</h2>
          <hr>

          <p>Unicode 是字体在网页端最原始的应用方式，特点是：</p>
          <ul>
            <li>支持按字体的方式去动态调整图标大小，颜色等等。</li>
            <li>默认情况下不支持多色，直接添加多色图标会自动去色。</li>
          </ul>
          <blockquote>
            <p>注意：新版 iconfont 支持两种方式引用多色图标：SVG symbol 引用方式和彩色字体图标模式。（使用彩色字体图标需要在「编辑项目」中开启「彩色」选项后并重新生成。）</p>
          </blockquote>
          <p>Unicode 使用步骤如下：</p>
          <h3 id="-font-face">第一步：拷贝项目下面生成的 <code>@font-face</code></h3>
<pre><code class="language-css"
>@font-face {
  font-family: 'iconfont';
  src: url('iconfont.woff2?t=1723449548742') format('woff2'),
       url('iconfont.woff?t=1723449548742') format('woff'),
       url('iconfont.ttf?t=1723449548742') format('truetype');
}
</code></pre>
          <h3 id="-iconfont-">第二步：定义使用 iconfont 的样式</h3>
<pre><code class="language-css"
>.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取字体编码，应用于页面</h3>
<pre>
<code class="language-html"
>&lt;span class="iconfont"&gt;&amp;#x33;&lt;/span&gt;
</code></pre>
          <blockquote>
            <p>"iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
          </blockquote>
          </div>
      </div>
      <div class="content font-class">
        <ul class="icon_lists dib-box">
          
          <li class="dib">
            <span class="icon iconfont icon-tuichudenglu"></span>
            <div class="name">
              退出登录
            </div>
            <div class="code-name">.icon-tuichudenglu
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fanhui"></span>
            <div class="name">
              返回
            </div>
            <div class="code-name">.icon-fanhui
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-weizhi"></span>
            <div class="name">
              位置
            </div>
            <div class="code-name">.icon-weizhi
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-modify"></span>
            <div class="name">
              修改
            </div>
            <div class="code-name">.icon-modify
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-add"></span>
            <div class="name">
              添加 加号 无边框
            </div>
            <div class="code-name">.icon-add
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-delete"></span>
            <div class="name">
              删除
            </div>
            <div class="code-name">.icon-delete
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-close"></span>
            <div class="name">
              关闭
            </div>
            <div class="code-name">.icon-close
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dayin"></span>
            <div class="name">
              打印
            </div>
            <div class="code-name">.icon-dayin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-date"></span>
            <div class="name">
              日期
            </div>
            <div class="code-name">.icon-date
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jia"></span>
            <div class="name">
              加
            </div>
            <div class="code-name">.icon-jia
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-daoru"></span>
            <div class="name">
              导入
            </div>
            <div class="code-name">.icon-daoru
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-left"></span>
            <div class="name">
              方向
            </div>
            <div class="code-name">.icon-left
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-right"></span>
            <div class="name">
              方向
            </div>
            <div class="code-name">.icon-right
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-user"></span>
            <div class="name">
              用户
            </div>
            <div class="code-name">.icon-user
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-password"></span>
            <div class="name">
              密码
            </div>
            <div class="code-name">.icon-password
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-location"></span>
            <div class="name">
              定位
            </div>
            <div class="code-name">.icon-location
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-rili"></span>
            <div class="name">
              日历
            </div>
            <div class="code-name">.icon-rili
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-ssj"></span>
            <div class="name">
              上三角
            </div>
            <div class="code-name">.icon-ssj
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-xsj"></span>
            <div class="name">
              下三角
            </div>
            <div class="code-name">.icon-xsj
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-export"></span>
            <div class="name">
              导出
            </div>
            <div class="code-name">.icon-export
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-check"></span>
            <div class="name">
              查询
            </div>
            <div class="code-name">.icon-check
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-co2"></span>
            <div class="name">
              二氧化碳
            </div>
            <div class="code-name">.icon-co2
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-jiankong"></span>
            <div class="name">
              监控视频
            </div>
            <div class="code-name">.icon-jiankong
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gongdanguanli"></span>
            <div class="name">
              工单管理
            </div>
            <div class="code-name">.icon-gongdanguanli
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-device"></span>
            <div class="name">
              设备
            </div>
            <div class="code-name">.icon-device
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-senlin"></span>
            <div class="name">
              森林
            </div>
            <div class="code-name">.icon-senlin
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-gaojing"></span>
            <div class="name">
              告警
            </div>
            <div class="code-name">.icon-gaojing
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-baobiao"></span>
            <div class="name">
              报表
            </div>
            <div class="code-name">.icon-baobiao
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-mei"></span>
            <div class="name">
              煤炭生产
            </div>
            <div class="code-name">.icon-mei
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-down"></span>
            <div class="name">
              方向
            </div>
            <div class="code-name">.icon-down
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-up"></span>
            <div class="name">
              方向
            </div>
            <div class="code-name">.icon-up
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-dianzhan"></span>
            <div class="name">
              电站数量
            </div>
            <div class="code-name">.icon-dianzhan
            </div>
          </li>
          
          <li class="dib">
            <span class="icon iconfont icon-fenxi"></span>
            <div class="name">
              分析
            </div>
            <div class="code-name">.icon-fenxi
            </div>
          </li>
          
        </ul>
        <div class="article markdown">
        <h2 id="font-class-">font-class 引用</h2>
        <hr>

        <p>font-class 是 Unicode 使用方式的一种变种，主要是解决 Unicode 书写不直观，语意不明确的问题。</p>
        <p>与 Unicode 使用方式相比，具有如下特点：</p>
        <ul>
          <li>相比于 Unicode 语意明确，书写更直观。可以很容易分辨这个 icon 是什么。</li>
          <li>因为使用 class 来定义图标，所以当要替换图标时，只需要修改 class 里面的 Unicode 引用。</li>
        </ul>
        <p>使用步骤如下：</p>
        <h3 id="-fontclass-">第一步：引入项目下面生成的 fontclass 代码：</h3>
<pre><code class="language-html">&lt;link rel="stylesheet" href="./iconfont.css"&gt;
</code></pre>
        <h3 id="-">第二步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;span class="iconfont icon-xxx"&gt;&lt;/span&gt;
</code></pre>
        <blockquote>
          <p>"
            iconfont" 是你项目下的 font-family。可以通过编辑项目查看，默认是 "iconfont"。</p>
        </blockquote>
      </div>
      </div>
      <div class="content symbol">
          <ul class="icon_lists dib-box">
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-tuichudenglu"></use>
                </svg>
                <div class="name">退出登录</div>
                <div class="code-name">#icon-tuichudenglu</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fanhui"></use>
                </svg>
                <div class="name">返回</div>
                <div class="code-name">#icon-fanhui</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-weizhi"></use>
                </svg>
                <div class="name">位置</div>
                <div class="code-name">#icon-weizhi</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-modify"></use>
                </svg>
                <div class="name">修改</div>
                <div class="code-name">#icon-modify</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-add"></use>
                </svg>
                <div class="name">添加 加号 无边框</div>
                <div class="code-name">#icon-add</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-delete"></use>
                </svg>
                <div class="name">删除</div>
                <div class="code-name">#icon-delete</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-close"></use>
                </svg>
                <div class="name">关闭</div>
                <div class="code-name">#icon-close</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dayin"></use>
                </svg>
                <div class="name">打印</div>
                <div class="code-name">#icon-dayin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-date"></use>
                </svg>
                <div class="name">日期</div>
                <div class="code-name">#icon-date</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jia"></use>
                </svg>
                <div class="name">加</div>
                <div class="code-name">#icon-jia</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-daoru"></use>
                </svg>
                <div class="name">导入</div>
                <div class="code-name">#icon-daoru</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-left"></use>
                </svg>
                <div class="name">方向</div>
                <div class="code-name">#icon-left</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-right"></use>
                </svg>
                <div class="name">方向</div>
                <div class="code-name">#icon-right</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-user"></use>
                </svg>
                <div class="name">用户</div>
                <div class="code-name">#icon-user</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-password"></use>
                </svg>
                <div class="name">密码</div>
                <div class="code-name">#icon-password</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-location"></use>
                </svg>
                <div class="name">定位</div>
                <div class="code-name">#icon-location</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-rili"></use>
                </svg>
                <div class="name">日历</div>
                <div class="code-name">#icon-rili</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-ssj"></use>
                </svg>
                <div class="name">上三角</div>
                <div class="code-name">#icon-ssj</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-xsj"></use>
                </svg>
                <div class="name">下三角</div>
                <div class="code-name">#icon-xsj</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-export"></use>
                </svg>
                <div class="name">导出</div>
                <div class="code-name">#icon-export</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-check"></use>
                </svg>
                <div class="name">查询</div>
                <div class="code-name">#icon-check</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-co2"></use>
                </svg>
                <div class="name">二氧化碳</div>
                <div class="code-name">#icon-co2</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-jiankong"></use>
                </svg>
                <div class="name">监控视频</div>
                <div class="code-name">#icon-jiankong</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gongdanguanli"></use>
                </svg>
                <div class="name">工单管理</div>
                <div class="code-name">#icon-gongdanguanli</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-device"></use>
                </svg>
                <div class="name">设备</div>
                <div class="code-name">#icon-device</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-senlin"></use>
                </svg>
                <div class="name">森林</div>
                <div class="code-name">#icon-senlin</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-gaojing"></use>
                </svg>
                <div class="name">告警</div>
                <div class="code-name">#icon-gaojing</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-baobiao"></use>
                </svg>
                <div class="name">报表</div>
                <div class="code-name">#icon-baobiao</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-mei"></use>
                </svg>
                <div class="name">煤炭生产</div>
                <div class="code-name">#icon-mei</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-down"></use>
                </svg>
                <div class="name">方向</div>
                <div class="code-name">#icon-down</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-up"></use>
                </svg>
                <div class="name">方向</div>
                <div class="code-name">#icon-up</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-dianzhan"></use>
                </svg>
                <div class="name">电站数量</div>
                <div class="code-name">#icon-dianzhan</div>
            </li>
          
            <li class="dib">
                <svg class="icon svg-icon" aria-hidden="true">
                  <use xlink:href="#icon-fenxi"></use>
                </svg>
                <div class="name">分析</div>
                <div class="code-name">#icon-fenxi</div>
            </li>
          
          </ul>
          <div class="article markdown">
          <h2 id="symbol-">Symbol 引用</h2>
          <hr>

          <p>这是一种全新的使用方式，应该说这才是未来的主流，也是平台目前推荐的用法。相关介绍可以参考这篇<a href="">文章</a>
            这种用法其实是做了一个 SVG 的集合，与另外两种相比具有如下特点：</p>
          <ul>
            <li>支持多色图标了，不再受单色限制。</li>
            <li>通过一些技巧，支持像字体那样，通过 <code>font-size</code>, <code>color</code> 来调整样式。</li>
            <li>兼容性较差，支持 IE9+，及现代浏览器。</li>
            <li>浏览器渲染 SVG 的性能一般，还不如 png。</li>
          </ul>
          <p>使用步骤如下：</p>
          <h3 id="-symbol-">第一步：引入项目下面生成的 symbol 代码：</h3>
<pre><code class="language-html">&lt;script src="./iconfont.js"&gt;&lt;/script&gt;
</code></pre>
          <h3 id="-css-">第二步：加入通用 CSS 代码（引入一次就行）：</h3>
<pre><code class="language-html">&lt;style&gt;
.icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
}
&lt;/style&gt;
</code></pre>
          <h3 id="-">第三步：挑选相应图标并获取类名，应用于页面：</h3>
<pre><code class="language-html">&lt;svg class="icon" aria-hidden="true"&gt;
  &lt;use xlink:href="#icon-xxx"&gt;&lt;/use&gt;
&lt;/svg&gt;
</code></pre>
          </div>
      </div>

    </div>
  </div>
  <script>
  $(document).ready(function () {
      $('.tab-container .content:first').show()

      $('#tabs li').click(function (e) {
        var tabContent = $('.tab-container .content')
        var index = $(this).index()

        if ($(this).hasClass('active')) {
          return
        } else {
          $('#tabs li').removeClass('active')
          $(this).addClass('active')

          tabContent.hide().eq(index).fadeIn()
        }
      })
    })
  </script>
</body>
</html>
