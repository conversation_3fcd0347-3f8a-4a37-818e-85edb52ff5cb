<template>
  <a-modal
    :visible="visible"
    :title="isEdit ? '编辑账户' : '新增账户'"
    @ok="handleOk"
    ok-text="确认"
    cancel-text="取消"
    @cancel="handleCancel"
  >
    <a-form
      :model="formState"
      name="basic"
      :label-col="{ span: 8 }"
      :wrapper-col="{ span: 16 }"
      autocomplete="off"
      @finish="onFinish"
      @finishFailed="onFinishFailed"
    >
      <a-form-item label="账户ID" name="advertiser_id" :rules="[{ required: true, message: '请输入账户ID' }]">
        <a-input v-model:value="formState.advertiser_id" />
      </a-form-item>
      <a-form-item label="账户名字" name="advertiser_name" :rules="[{ required: true, message: '请输入账户名字' }]">
        <a-input v-model:value="formState.advertiser_name" />
      </a-form-item>
      <a-form-item label="ECPM值" name="ecpm" :rules="[{ required: true, message: '请输入ECPM值' }]">
        <a-input v-model:value="formState.ecpm" />
      </a-form-item>
      <a-form-item label="视频数" name="exCnt" :rules="[{ required: true, message: '请输入视频数' }]">
        <a-input v-model:value="formState.exCnt" />
      </a-form-item>
      <a-form-item label="对应APPID" name="appId" :rules="[{ required: true, message: '请输入对应APPID' }]">
        <a-input v-model:value="formState.appId" />
      </a-form-item>
      <a-form-item label="是否启用" name="on_off" value-prop-name="checked">
        <a-switch v-model:checked="formState.on_off" />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, ref, computed } from 'vue';
import { Form, Modal, Input, Switch } from 'ant-design-vue';
import { AddPutInAccount, EditPutInAccount } from '@/api/api'; // 假设 AddPutInAccount 和 EditPutInAccount 函数在 api.ts 中定义
import { useStore } from 'vuex'; // 假设你使用 Vuex 进行状态管理
import { message } from "ant-design-vue";
interface FormState {
  advertiser_id: string;
  advertiser_name: string;
  ecpm: string;
  exCnt: string;
  appId: string;
  on_off: boolean;
}

export default defineComponent({
  components: {
    'a-modal': Modal,
    'a-form': Form,
    'a-form-item': Form.Item,
    'a-input': Input,
    'a-switch': Switch
  },
  setup(props, { emit }) {
    const store = useStore();
    const visible = ref(false);
    const isEdit = ref(false);

    const userData = computed(() => store.getters.userData);
    const formState = ref<FormState>({
      advertiser_id: '',
      advertiser_name: '',
      ecpm: '',
      exCnt: '',
      appId: '',
      on_off: false
    });

    const showModal = (isEditParam: boolean, initialValues: Partial<FormState> = {}) => {
      isEdit.value = isEditParam;
      visible.value = true;
      formState.value = { ...formState.value, ...initialValues };
    };

    const resetForm = () => {
      formState.value = {
        advertiser_id: '',
        advertiser_name: '',
        ecpm: '',
        exCnt: '',
        appId: '',
        on_off: false
      };
    };

    const handleOk = async () => {
      console.log('formState:', formState.value);
      try {
        const response = isEdit.value
          ? await EditPutInAccount(
              userData.value.account, // 假设 account 是从 store 中获取的
              formState.value.advertiser_id,
              parseFloat(formState.value.ecpm),
              parseInt(formState.value.exCnt),
              formState.value.appId,
              formState.value.advertiser_name,
              formState.value.on_off ? 1 : 0
            )
          : await AddPutInAccount(
              userData.value.account, // 假设 account 是从 store 中获取的
              formState.value.advertiser_id,
              parseFloat(formState.value.ecpm),
              parseInt(formState.value.exCnt),
              formState.value.appId,
              formState.value.advertiser_name,
              formState.value.on_off ? 1 : 0
            );
        console.log('操作成功:', response);
        message.success(isEdit.value? '编辑成功!' : '新增成功!');
        visible.value = false;
        emit('refreshList'); // 通知父组件刷新列表数据
      } catch (error) {
        console.error('操作失败:', error);
        message.error(isEdit.value? '编辑失败!' : '新增失败!');
        visible.value = false;
      }
    };

    const handleCancel = () => {
      visible.value = false;
    };

    const onFinish = (values: any) => {
      console.log('表单提交成功:', values);
    };

    const onFinishFailed = (errorInfo: any) => {
      console.log('表单提交失败:', errorInfo);
    };

    return {
      visible,
      isEdit,
      formState,
      showModal,
      resetForm,
      handleOk,
      handleCancel,
      onFinish,
      onFinishFailed
    };
  }
});
</script>

<style scoped>
/* 添加一些样式 */
</style>
