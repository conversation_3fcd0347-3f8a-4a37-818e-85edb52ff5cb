import { BackStageApi } from "./api/BackStageApi";

export interface NavDataItem {
  NavID: string;
  Icons: string;
  Title: string;
  Path: string;
  Name: string;
  Child?: NavDataItem[];
}

export function changeGameListApiDataToNavData(index: number, apiData: BackStageApi.Post.GetGameList.TGameItem): NavDataItem { 
  return {
    NavID: `2-${index+1}`,
    Icons: 'ReadOutlined',
    Title: apiData.name,
    Path: `/dataManage/${apiData.appid}`,
    Name: 'dataManage',
    Child: []
  }  
}

export const NavDataInfo: NavDataItem[] = [
  {
    NavID: '0',
    Icons: 'HomeOutlined',
    Title: '首页',
    Path: '/',
    Name: 'Home',
    Child: []
  },
  {
    NavID: '1',
    Icons: 'BulbOutlined',
    Title: '直投账户',
    Path: '/runtimeData',
    Name: 'runtimeData',
    Child: []
  },
  {
    NavID: '2',
    Icons: 'LaptopOutlined',
    Title: '用户管理',
    Path: '/dataManage/1',
    Name: 'dataManage',
    Child: [
      {
        NavID: '2-1',
        Icons: 'ReadOutlined',
        Title: '游戏名1',
        Path: '/dataManage/1',
        Name: 'dataManage',
        Child: []
      },
      {
        NavID: '2-2',
        Icons: 'ReadOutlined',
        Title: '游戏名2',
        Path: '/dataManage/2',
        Name: 'dataManage',
        Child: []
      },
      {
        NavID: '2-3',
        Icons: 'ReadOutlined',
        Title: '游戏名3',
        Path: '/dataManage/3',
        Name: 'dataManage',
        Child: []
      },
    ]
  },
  {
    NavID: '3',
    Icons: 'FundProjectionScreenOutlined',
    Title: '数据分析',
    Path: '/dataAnalysis',
    Name: 'dataAnalysis',
    Child: []
  }
];