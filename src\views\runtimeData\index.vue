<template>
  <a-spin :spinning="spinning">
    <div class="export-btn">
      <a-button type="primary" @click="handleAdd" class="btn-right">
        <template #icon><PlusOutlined /></template>
        新增
      </a-button>
      <a-button type="danger" @click="handleDeleteMore()" :disabled="selectedRowKeys.length === 0" >
        <template #icon><DeleteOutlined /></template>
        删除
      </a-button>
    </div>
    <a-table
      :columns="columns"
      :data-source="data"
      :pagination="pagination"
      :row-key="record => record.advertiser_id"
      bordered
      size="small"
      :row-selection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
    >
      <template #bodyCell="{ column, record }">
        <span v-if="column.dataIndex === 'on_off'">
          <a-switch v-model:checked="record.on_off" @change="handleSwitchChange(record)"/>
        </span>
        <span v-else-if="column.dataIndex === 'operation'">
          <a-button type="primary" ghost @click="handleEdit(record)" size="small" class="btn-right">
            <template #icon><EditOutlined /></template>
            编辑
          </a-button>
          <a-button type="danger" ghost @click="handleDelete(record)" size="small">
            <template #icon><DeleteOutlined /></template>
            删除
          </a-button>
        </span>
      </template>
    </a-table>
     <AddEdit ref="addEditModal" @refreshList="refreshList"  />
    <a-modal v-model:visible="deleteModalVisible" :title="deleteType === 'single' ? '删除数据' : '批量删除数据'" @ok="handleOk" okText="确认" cancelText="取消">
      <p style="color: red">确认删除所选数据吗？</p>
    </a-modal>
  </a-spin>
</template>

<script lang="ts">
import { defineComponent, onMounted, computed, ref } from "vue";
import { GetPutInAccountList, DeletePutInAccount, UpdatePutInAccount } from "@/api/api";
import { useStore } from "vuex";
import { useRoute } from "vue-router";
import { message, Modal  } from "ant-design-vue";
import { PlusOutlined, DeleteOutlined, EditOutlined } from "@ant-design/icons-vue";
import AddEdit from './addEdit.vue'; // 确保路径正确

export default defineComponent({
  components: {
    PlusOutlined,
    DeleteOutlined,
    EditOutlined,
    AddEdit,
  },
  computed: {
  },
  setup() {
    const store = useStore();
    const route = useRoute();

    const userData = computed(() => store.getters.userData);
    const deleteModalVisible = ref(false);
    const deleteRecord = ref({});
    const deleteType = ref("single");
    const spinning = ref(true);
    const columns = ref([
      {
        title: "序号",
        dataIndex: "uuid",
        width: 60,
      },
      {
        title: "账户ID",
        dataIndex: "advertiser_id",
        width: 200,
      },
      {
        title: "ecpm条件",
        dataIndex: "ecpm",
      },
      {
        title: "视频数",
        dataIndex: "exCnt",
      },
      {
        title: "APPID",
        dataIndex: "appId",
      },
      {
        title: "账户名称",
        dataIndex: "advertiser_name",
      },
      {
        title: "是否启用",
        dataIndex: "on_off",
      },
      {
        title: '操作',
        dataIndex: 'operation',
        fixed: 'right',
      },
    ]);

    const data = ref([]);
    const pagination = ref({
      current: 1,
      pageSize: 10,
      total: 0,
      showSizeChanger: true,
      showQuickJumper: true,
      showTotal: (total: number) => `共 ${total} 条`,
      onChange: (page: number, pageSize?: number) => {
        pagination.value.current = page;
        pagination.value.pageSize = pageSize || 10;
        fetchData();
      },
      onShowSizeChange: (current: number, size: number) => {
        pagination.value.current = 1;
        pagination.value.pageSize = size;
        fetchData();
      },
    });

    const selectedRowKeys = ref([]);

    const onSelectChange = (selectedKeys: any) => {
      selectedRowKeys.value = selectedKeys;
    };

    const fetchData = async () => {
      try {
        const res = await GetPutInAccountList(
          userData.value.account,
          pagination.value.current,
          pagination.value.pageSize,
        );
        console.log(res, "获取直投账号成功");
        spinning.value = false;
        data.value = res.data.list.map((item: any) => ({
          ...item,
          on_off: item.on_off === 1, // 将 on_off 转换为布尔值
        }));
        pagination.value.total = res.data.total;
      } catch (error) {
        spinning.value = false;
        console.error(error, "获取直投账号失败");
        message.error("获取直投账号失败");
      }
    };

    const addEditModal = ref(null);

    const handleAdd = () => {
      console.log("新增");
      (addEditModal.value as any).resetForm(); // 调用 resetForm 方法清空表单数据
      (addEditModal.value as any).showModal(false); // 调用 addEdit 组件的 showModal 方法
    };

    const handleEdit = (record: any) => {
      console.log("编辑", record);
      (addEditModal.value as any).showModal(true, record); // 调用 addEdit 组件的 showModal 方法并传入编辑的初始值
    };

    const handleDelete = async (record: any) => {
      deleteModalVisible.value = true;
      deleteRecord.value = record;
      deleteType.value = "single";
    };

    const handleOk = async () => {
      deleteModalVisible.value = false;
      if (deleteType.value === "single") {
        try {
          await DeletePutInAccount(userData.value.account, (deleteRecord.value as any).advertiser_id);
          message.success("删除成功");
          fetchData(); // 刷新数据
        } catch (error) {
          console.error(error, "删除失败");
          message.error("删除失败");
        }
      } else {
        try {
          await DeletePutInAccount(userData.value.account,selectedRowKeys.value.join(","));
          message.success("删除成功");
          fetchData(); // 刷新数据
        } catch (error) {
          console.error(error, "删除失败");
          message.error("删除失败");
        }
      }
    };

    const handleDeleteMore = async () => {
      console.log("批量删除", selectedRowKeys.value);
      deleteType.value = "more";
      // 批量删除
       if (selectedRowKeys.value.length === 0) {
        message.warning("请选择要删除的记录");
        return;
      }
      deleteModalVisible.value = true;

    };

    const refreshList = () => {
      console.log("刷新列表数据");
      // 在这里添加刷新列表数据的逻辑
      fetchData();
    };

    const handleSwitchChange = async (record: any) => {
  try {
    await UpdatePutInAccount(
      userData.value.account,
      record.advertiser_id,
      record.on_off ? 1 : 0, 
    );
    message.success("是否启用更新成功!");
    fetchData(); // 刷新数据
  } catch (error) {
    console.error(error, "是否启用更新失败!");
    message.error("是否启用更新失败!");
    // 恢复原来的状态
    record.on_off = !record.on_off;
  }
};

    onMounted(() => {
      fetchData();
    });

    return { columns, data,spinning, pagination, deleteModalVisible, deleteRecord, deleteType,addEditModal, handleAdd, handleEdit, handleDelete, refreshList, selectedRowKeys, onSelectChange,handleSwitchChange,handleOk,handleDeleteMore };
  },
});
</script>

<style lang="scss" scoped>
.export-btn {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 10px;
}
.btn-right {
    margin-right: 10px;
}
</style>
