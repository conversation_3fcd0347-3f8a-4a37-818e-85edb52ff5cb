export module BackStageApi {
    export const root = "/back_stage";
    //
    export module Post {
        //登录接口
        export module Login {
            export const router = root + "/login";
            export type request = {
                account: string;
                password: string;
            };
            export type response = {
                account: string;
                name: string;
                token: string;
                power: number;
            };
        }
        //登出接口
        export module Logout {
            export const router = root + "/logout";
            export type headers = {
                token: string;
            };
            export type request = {
                account: string;
            };
            export type response = {};
        }
        //手动添加/注册用户接口
        export module Register {
            export enum EPower {
                //无权限
                Nil = 0,
                //管理员
                Master = 1,
                //超级管理员
                SuperMaster = 2,
            }

            export const router = root + "/register";
            export type headers = {
                token: string;
            };
            export type request = {
                account: string;
                //注册的账号信息
                registerAct: string; //账号
                registerPwd: string; //密码
                nickName: string; //名字
                power: EPower; //权限
            };

            export type response = {};
        }
        //获取游戏列表
        export module GetGameList {
            export type TGameItem = {
                appid: string;
                index: number;
                name: string;
            };
            export const router = root + "/gameList";
            export type headers = {
                token: string;
            };
            export type request = {
                account: string;
            };
            export type response = {
                list: TGameItem[];
            };
        }
        //手动上报
        export module ReportEcpmPoint {
            export const router = root + "/reportEcpmPoint";
            export type headers = {
                token: string;
            };
            export type request = {
                account: string;
                appid: string;
                openid: string;
                ed: 1 | 0; //是否已上报
            };
            export type response = {};
        }
        //获取用户列表
        export module getGameUserList {
            export type TGameUser = {
                uuid: number; //序号
                open_id: string; //用户id
                aid: string; //广告计划id
                advertiser_id: string; //广告主id
                project_id: string; //项目id
                promotion_id: string; //广告id
                click_id: string; //点击id
                key_ed: number; //是否已上报
                os: number; //平台
                android_id: string; //安卓ID
                imei: string; //安卓设备md5
                aveEcpm: number; //平均ecpm
                exposureCnt: number; //曝光次数
                ecpm: number;    //总ecpm
                time_stamp: number; //时间
            };
            export const router = root + "/userList";
            export type headers = {
                token: string;
            };
            //查询条件
            export type TWhere = {
                open_id?: string, //用户id
                project_id?: string,//项目id
                promotion_id?: string, //广告id
                start_time?: string, //开始时间
                end_time?: string, //结束时间
            }
            export type request = {
                account: string;
                appid: string;
                start: number; //当前页
                limit: number; //每页数量
                order: string; //排序方式
                desc: boolean; //是否倒序
            } & TWhere;
            export type response = {
                total: number; //总页数
                list: TGameUser[];
            };
        }
        //获取直投账号列表
        export module GetPutInAccountList {
            export type TPutInAccount = {
                uuid: number;
                advertiser_id: string;
                ecpm: number;
                exCnt: number;
                appId?: string;
                advertiser_name: string;
                on_off?: number;
            };
            export const router = root + "/getPutInAccountList";
            export type headers = {
                token: string;
            };
            export type request = {
                account: string;
                page: number;
                pageSize: number;
            };
            export type response = {
                total: number;
                list: TPutInAccount[];
            };
        }
        //添加直投账号
        export module AddPutInAccount {
            export const router = root + "/addPutInAccount";
            export type headers = {
                token: string;
            }
            export type request =
                { account: string } &
                Omit<GetPutInAccountList.TPutInAccount, 'uuid'>;
            export type response = {};
        }
        //修改直投账号
        export module EditPutInAccount {
            export const router = root + "/editPutInAccount";
            export type headers = {
                token: string;
            }
            export type request =
                { account: string } &
                Partial<GetPutInAccountList.TPutInAccount>;
            export type response = {};
        }
        //删除直投账号
        export module DeletePutInAccount {
            export const router = root + "/deletePutInAccount";
            export type headers = {
                token: string;
            }
            export type request = {
                account: string;
                advertiser_id: string;
            };
            export type response = {};
        }
    }
}









































