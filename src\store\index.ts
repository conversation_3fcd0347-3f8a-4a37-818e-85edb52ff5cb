import { createStore } from 'vuex'
import axios, { AxiosInstance, InternalAxiosRequestConfig } from 'axios';
import { BackStageApi } from '@/api/BackStageApi';
import persistencePlugin from './plugins/persistence';
import { State, UserData } from './state';

// 创建 axios 实例
const instance: AxiosInstance = axios.create({
  baseURL: 'https://back.tobetopone.com', // 正式环境
  // baseURL: 'http://apple.tobetopone.com', // 测试环境
  // baseURL: 'http://192.168.0.106:10110', // 你的服务器地址
  timeout: 10000, // 请求超时时间
});

// 请求拦截器
instance.interceptors.request.use(
  (config: InternalAxiosRequestConfig) => {
    // 在发送请求之前做些什么，例如添加认证头
    const token = localStorage.getItem('token');
    if (token) {
      config.headers['token'] = `${token}`;
    }
    return config;
  },
  (error) => {
    // 对请求错误做些什么
    return Promise.reject(error);
  }
);

export default createStore({
  state: {
    isAuthenticated: false,
    userData: {} as UserData,
  } as State,
  mutations: {
    setAuthenticated(state, status: boolean) {
      state.isAuthenticated = status;
    },
    setUserData(state, userData: UserData) { // 添加 setUserData mutation
      state.userData = userData;
    }
  },
  actions: {
    async login({ commit }, credentials: BackStageApi.Post.Login.request) {
      try {
        const route = BackStageApi.Post.Login.router;
        // 发送登录请求
        const response = await instance.post(route, credentials);
        console.log(response, '登录接口');
        if (response.status !== 200) {
          console.error('Login failed:', response.status, response.statusText);
          throw new Error('登录失败'); // 抛出错误
        }
        const res = response.data as { err: number, msg: string, data: BackStageApi.Post.Login.response };
        if (res.err != 0) {
          console.error('Login failed:', res.msg);
          throw new Error('登录失败'); // 抛出错误
        }
        const token = res.data.token;
        console.log('Login success:', res);
    
        // 存储 token 并设置认证状态
        localStorage.setItem('token', token);
        // 存储用户数据
        commit('setUserData', res.data);
        commit('setAuthenticated', true);
        return res; // 返回登录结果
      } catch (error) {
        throw new Error('登录失败'); // 抛出错误
      }
    }
  },
  getters: {
    isAuthenticated: state => state.isAuthenticated,
    userData: state => state.userData
  },
  plugins: [persistencePlugin]
})
