{"name": "zero-one-management", "version": "0.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "run-p type-check \"build-only {@}\" --", "preview": "vite preview", "test:unit": "vitest", "build-only": "vite build", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/"}, "dependencies": {"@types/axios": "^0.14.0", "@types/moment": "^2.13.0", "ant-design-vue": "^3.2.15", "axios": "^1.7.4", "moment": "^2.30.1", "vue": "^3.4.29", "vue-router": "^4.3.3", "vuex": "^4.0.2", "vuex-persistedstate": "^4.1.0"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/jsdom": "^21.1.7", "@types/node": "^20.14.15", "@vitejs/plugin-vue": "^5.1.2", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/test-utils": "^2.4.6", "@vue/tsconfig": "^0.5.1", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "jsdom": "^24.1.0", "npm-run-all2": "^6.2.0", "prettier": "^3.2.5", "sass": "^1.77.8", "typescript": "~5.4.0", "vitest": "^1.6.0", "vue-tsc": "^2.0.21"}}