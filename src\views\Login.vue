<template>
  <div class="logo-container">
    <div class="background-blur"></div>
    <div class="logo-main">
      <h2>欢迎登录系统</h2>
      <a-form
        ref="loginForm"
        :model="formState"
        name="basic"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 16 }"
        autocomplete="off"
        @finish="onFinish"
        @finishFailed="onFinishFailed"
      >
        <a-form-item
          label="用户名"
          name="account"
          :rules="[{ required: true, message: '请输入用户名!' }]"
        >
          <a-input v-model:value="formState.account" />
        </a-form-item>

        <a-form-item
          label="密码"
          name="password"
          :rules="[{ required: true, message: '请输入密码!' }]"
        >
          <a-input-password v-model:value="formState.password" />
        </a-form-item>

        <!-- <a-form-item name="remember" :wrapper-col="{ offset: 8, span: 16 }">
          <a-checkbox v-model:checked="formState.remember">记住我</a-checkbox>
        </a-form-item> -->

        <a-form-item :wrapper-col="{ offset: 8, span: 16 }">
          <a-button type="primary" html-type="submit" :loading="loading">提交</a-button>
          <a-button style="margin-left: 10px" @click="resetForm">重置</a-button>
        </a-form-item>
      </a-form>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { Form } from 'ant-design-vue'
import { useStore } from 'vuex'
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue'

interface FormState {
  account: string
  password: string
  // remember: boolean
}

const store = useStore()
const router = useRouter(); // 确保正确导入和使用 router
const formState = reactive<FormState>({
  account: '',
  password: ''
  // remember: true
})

const loading = ref(false)
const loginForm = ref()

const onFinish = async (values: any) => {
  loading.value = true;
  try {
    const loginResult = await store.dispatch('login', values);
    console.log('登录结果:', loginResult); // 添加日志查看登录结果
    if (loginResult && loginResult.err === 0) {
      message.success('登录成功');
      console.log('登录成功');
      // 处理登录成功后的逻辑，例如跳转到首页
      router.push('/');
    } else {
      message.error('登录失败，请检查用户名和密码');
    }
  } catch (error) {
    console.error('登录失败:', error); // 添加日志查看错误信息
    message.error('登录失败，请检查用户名和密码');
  } finally {
    loading.value = false;
  }
};

const onFinishFailed = (errorInfo: any) => {
  console.log('Failed:', errorInfo)
}

const resetForm = () => {
  loginForm.value.resetFields()
}
</script>

<style lang="scss" scoped>
.logo-container {
  width: 100vw;
  height: 100vh;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  .background-blur {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('../assets/img/bg.png') no-repeat;
    background-size: 100% 100%;
    filter: blur(10px);
    z-index: 0;
  }
  .logo-main {
    background: rgba(255, 255, 255, 0.9);
    border-radius: 10px;
    padding: 40px 80px;
    box-shadow: 0px 2px 3px rgba(0, 0, 0, 0.1);
    z-index: 1;
    h2 {
      text-align: center;
      margin-bottom: 30px;
    }
  }
}
</style>
