<template>
  <a-layout id="components-layout-demo-custom-trigger">
    <a-layout-sider v-model:collapsed="collapsed" collapsible>
      <div class="logo">
        <img src="@/assets/img/logo.png" alt="">
        <h2>零一直投管理系统</h2>
      </div>

      <a-menu :inline-collapsed="collapsed" :selectedKeys="[route.path]" theme="dark" mode="inline">
        <template v-for="item in navData">
          <a-sub-menu :key="item.Path" v-if="item.Child && item.Child.length > 0">
            <template #icon>
              <component :is="item.Icons"></component>
            </template>
            <template #title>
              <span>{{ item.Title }}</span>
            </template>
            <a-menu-item v-for="itChild in item.Child" :key="itChild.Path">
              <template #icon>
                <component :is="itChild.Icons"></component>
              </template>
              <router-link :to="itChild.Path">
                {{ itChild.Title }}
              </router-link>
            </a-menu-item>
          </a-sub-menu>

          <a-menu-item :key="item.Path" v-else>
            <template #icon>
              <component :is="item.Icons"></component>
            </template>
            <router-link :to="item.Path">
              <span>{{ item.Title }}</span>
            </router-link>
          </a-menu-item>
        </template>
      </a-menu>
    </a-layout-sider>
    <a-layout>
      <a-layout-header class="layout-header white padding0">
        <div id="header">
          <div id="left">
            欢迎亲爱的<span style="color: red">
              {{
                userData.power === 0 ? '用户' : userData.power === 1 ? '管理员' : '超级管理员'
              }} </span
            >访问系统
          </div>
          <div id="right">
            <a-avatar style="background-color: #87d068; margin-right: 10px">
              <template #icon>
                <UserOutlined />
              </template>
            </a-avatar>
            <a-dropdown>
              <span>
                {{ userData.name }}
                <DownOutlined />
              </span>
              <template #overlay>
                <a-menu>
                  <a-menu-item><router-link to="/showUserInfo">我的信息</router-link></a-menu-item>
                  <a-menu-item><router-link to="/editUserInfo">修改信息</router-link></a-menu-item>
                  <a-menu-item><span @click="showLogoutModal = true">退出登录</span></a-menu-item>
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </div>
      </a-layout-header>
      <a-layout-content style="margin: 0 16px">
        <a-breadcrumb style="margin: 16px 0" separator=">">
          <a-breadcrumb-item @click="goback">
            <a-icon>
              <import-outlined />
            </a-icon>
            返回
          </a-breadcrumb-item>
          <a-breadcrumb-item v-for="(item, index) in breadList" :key="item.name">
            <router-link
              v-if="item.name !== name && index !== 1"
              :to="{ path: item.path === '' ? '/' : item.path }"
            >
              {{ item.meta.title }}
            </router-link>
            <span v-else>
              {{ item.meta.title }}
            </span>
          </a-breadcrumb-item>
        </a-breadcrumb>
        <div class="content">
          <router-view />
        </div>
      </a-layout-content>
      <a-layout-footer style="text-align: center">
        Copyright © 长沙零一互动科技有限公司版权所有 湘ICP备2021015712号-1 湘B2-20210409
      </a-layout-footer>
    </a-layout>

    <!-- 退出登录的 a-modal -->
    <a-modal
      v-model:visible="showLogoutModal"
      title="退出登录"
      okText="确定"
      cancelText="取消"
      @ok="handleLogout"
      @cancel="showLogoutModal = false"
    >
      <div style="color: red">您确定要退出登录吗？点击确定账号则将退出！</div>
    </a-modal>
  </a-layout>
</template>

<script setup lang="ts">
import { ref, onBeforeMount, watch, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { message } from 'ant-design-vue'
import { DownOutlined, ImportOutlined } from '@ant-design/icons-vue'
import myRouter from '@/router'
import { NavDataInfo } from '../../navDataInfo'
import { useStore } from 'vuex'
import { getGameList, logout } from '@/api/api'

const navData = ref(NavDataInfo)
const route = useRoute()
const router = useRouter()
const store = useStore() // 确保 store 已正确导入
const collapsed = ref(false)
const name = ref('')
const breadList = ref<Array<any>>([])
const showLogoutModal = ref(false)

const userData = computed(() => store.getters.userData)
console.log(userData.value, 'userData')

const getBreadcrumb = () => {
  breadList.value = []
  name.value = route.name ? route.name.toString() : ''
  route.matched.forEach((item) => {
    breadList.value.push(item)
  })
}

const goback = () => {
  router.back()
}

const handleLogout = async () => {
  try {
    await logout(userData.value.name)
    localStorage.removeItem('token')
    store.commit('setAuthenticated', false)
    message.success('登出成功')
    router.push('/login')
  } catch (error) {
    message.error('登出失败，请重试')
  }
}

watch(() => route.path, getBreadcrumb)

onBeforeMount(() => {
  getBreadcrumb()
})
onMounted(async () => {
  try {
    const res = await getGameList(userData.value.account)
    NavDataInfo[2].Child = res.list as any
    console.log(res, 'getGameList')
  } catch (error) {
    console.log(error, '获取游戏列表失败')
  }
})
</script>

<style scoped lang="scss">
#components-layout-demo-custom-trigger {
  height: 100%;
  min-height: 100vh;
}

#components-layout-demo-custom-trigger .logo {
  width: 60%;
  height: 102px;
  margin: 16px;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  img {
    width: 62px;
    height: 62px;
  }
  h2 {
    margin-top: 20px;
    font-family: 宋体;
    color: #faad14;
    font-size: 14px;
    text-align: center;
  }
  
}

#header {
  display: flex;
  height: 70px;
  padding: 0;
}

#left {
  width: 40%;
  justify-content: flex-start;
  display: flex;
  align-items: center;
  margin-left: 16px;
}

#right {
  flex: 1;
  width: 60%;
  justify-content: flex-end;
  display: flex;
  align-items: center;
  margin-right: 16px;
}

.content {
  padding: 24px;
  background: #fff;
  min-height: 100%;
}
</style>
